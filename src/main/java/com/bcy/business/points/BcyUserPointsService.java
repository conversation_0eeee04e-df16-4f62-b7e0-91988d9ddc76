package com.bcy.business.points;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcy.domain.form.points.AdjustUserPointsForm;
import com.bcy.domain.request.PageRequest;
import com.bcy.domain.response.points.UserPointsResponse;
import com.bcy.entity.points.BcyUserPointsEntity;
import com.bcy.entity.points.BcyUserPointsLogEntity;
import com.bcy.entity.sys.SysUserEntity;
import com.bcy.enums.PointsChangeTypeEnum;
import com.bcy.service.points.UserPointsLogService;
import com.bcy.service.points.UserPointsService;
import com.bcy.service.sys.IUserService;
import com.bcy.utils.PageUtils;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 业务层：用户积分
 */
@Service
public class BcyUserPointsService {
    @Resource
    private UserPointsService userPointsService;
    @Resource
    private IUserService userService;
    @Resource
    private UserPointsLogService userPointsLogService;

    /**
     * 用户积分分页列表
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    public PageUtils queryPage(PageRequest pageRequest) {
        if (StringUtils.isNotBlank(pageRequest.getName())) {
            pageRequest.setLinkId(BigDecimal.ZERO.longValue());
            List<SysUserEntity> userEntities = userService.listByNickName(pageRequest.getName());
            if (CollectionUtils.isNotEmpty(userEntities)) {
                pageRequest.setLinkId(userEntities.get(0).getId());
            }
        }
        IPage<BcyUserPointsEntity> iPage = userPointsService.queryPage(pageRequest);
        List<UserPointsResponse> responses = new ArrayList<>();
        List<BcyUserPointsEntity> pointsList = iPage.getRecords();
        if (CollectionUtils.isNotEmpty(pointsList)) {
            pointsList.forEach(item -> {
                UserPointsResponse response = new UserPointsResponse();
                BeanUtils.copyProperties(item, response);
                SysUserEntity user = userService.getById(item.getUserId());
                if (Objects.nonNull(user)) {
                    response.setUserName(user.getNickName());
                }
                responses.add(response);
            });
        }
        return new PageUtils(responses, iPage.getTotal(), pageRequest.getLimit(), pageRequest.getPage());
    }

    /**
     * 手动调整用户积分
     *
     * @param form 调整积分表单
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean adjustUserPoints(AdjustUserPointsForm form) {
        Long userId = form.getUserId();
        Integer points = form.getPoints();
        String remark = form.getRemark();
        // 查询用户积分信息
        BcyUserPointsEntity userPoints = userPointsService.searchUserPoints(userId);
        int beforePoints = 0;
        if (userPoints == null) {
            // 如果没有积分记录则新建
            userPoints = new BcyUserPointsEntity();
            userPoints.setUserId(userId);
            userPoints.setTotalPoints(points);
            userPointsService.save(userPoints);
        } else {
            // 积分调整
            beforePoints = userPoints.getTotalPoints();
            int newPoints = beforePoints + points;
            userPoints.setTotalPoints(newPoints);
            userPointsService.updateById(userPoints);
        }

        // 记录积分调整日志
        BcyUserPointsLogEntity log = new BcyUserPointsLogEntity();
        log.setUserId(userId);
        log.setChangeValue(form.getPoints());
        log.setRemark(StringUtils.isBlank(form.getRemark()) ? PointsChangeTypeEnum.ADMIN_ADJUST.getDesc() : form.getRemark());
        log.setChangeType(PointsChangeTypeEnum.ADMIN_ADJUST.getCode());
        log.setRemark(remark);
        log.setBeforePoints(beforePoints);
        log.setAfterPoints(beforePoints + points);
        log.setCreateTime(new Date());
        return userPointsLogService.save(log);
    }


}