package com.bcy.business.points;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.form.points.PointsGoodsForm;
import com.bcy.domain.form.points.PointsGoodsOrderForm;
import com.bcy.domain.request.PageRequest;
import com.bcy.domain.response.points.PointsGoodsResponse;
import com.bcy.entity.points.BcyPointsGoodsEntity;
import com.bcy.entity.points.BcyPointsGoodsOrderEntity;
import com.bcy.entity.points.BcyUserPointsEntity;
import com.bcy.enums.PointsGoodsOrderStatusEnum;
import com.bcy.exception.BcyServiceException;
import com.bcy.service.points.PointsGoodsOrderService;
import com.bcy.service.points.PointsGoodsService;
import com.bcy.service.points.UserPointsService;
import com.bcy.utils.BcyAssertUtils;
import com.bcy.utils.PageUtils;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 业务层：积分商品
 */
@Service
public class BcyPointsGoodsService {
    @Resource
    private PointsGoodsService pointsGoodsService;
    @Resource
    private PointsGoodsOrderService pointsGoodsOrderService;
    @Resource
    private UserPointsService userPointsService;

    /**
     * 积分商品分页列表
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    public PageUtils queryPage(PageRequest pageRequest) {
        IPage<BcyPointsGoodsEntity> iPage = pointsGoodsService.queryPage(pageRequest);
        List<PointsGoodsResponse> responses = new ArrayList<>();
        List<BcyPointsGoodsEntity> goodsList = iPage.getRecords();
        if (CollectionUtils.isNotEmpty(goodsList)) {
            goodsList.forEach(item -> {
                PointsGoodsResponse response = new PointsGoodsResponse();
                BeanUtils.copyProperties(item, response);
                List<BcyPointsGoodsOrderEntity> orders = pointsGoodsOrderService.listByGoodsId(item.getId());
                if (CollectionUtils.isNotEmpty(orders)) {
                    int totalQuantity = orders.stream().mapToInt(BcyPointsGoodsOrderEntity::getQuantity).sum();
                    response.setIsUseNumber(totalQuantity);
                }
                responses.add(response);
            });
        }
        return new PageUtils(responses, iPage.getTotal(), pageRequest.getLimit(), pageRequest.getPage());
    }

    /**
     * 新增商品
     *
     * @param pointsGoodsForm 新增商品请求对象
     * @return 是否新增成功
     */
    public boolean savePointsGoods(PointsGoodsForm pointsGoodsForm) {
        // 判断商品名称不能重复
        BcyPointsGoodsEntity goods = pointsGoodsService.getByName(pointsGoodsForm.getName());
        BcyAssertUtils.isNull(goods, String.format("商品名称已存在，%s不存在！", pointsGoodsForm.getName()));
        BcyPointsGoodsEntity entity = new BcyPointsGoodsEntity();
        BeanUtils.copyProperties(pointsGoodsForm, entity);
        return pointsGoodsService.save(entity);
    }

    /**
     * 批量删除积分商品
     *
     * @param idsForm id数组
     * @return true/false
     */
    public boolean deleteByIds(IdsForm idsForm) {
        return pointsGoodsService.removeBatchByIds(idsForm.getIds());
    }

    /**
     * 更新积分商品
     *
     * @param pointsGoodsForm 更新商品请求对象
     * @return 是否更新成功
     */
    public boolean updatePointsGoods(PointsGoodsForm pointsGoodsForm) {
        BcyPointsGoodsEntity entity = pointsGoodsService.getById(pointsGoodsForm.getId());
        BcyAssertUtils.isNull(entity, String.format("该商品%s不存在！", pointsGoodsForm.getId()));
        if (!entity.getName().equals(pointsGoodsForm.getName())) {
            // 判断商品名称不能重复
            BcyPointsGoodsEntity goods = pointsGoodsService.getByName(pointsGoodsForm.getName());
            BcyAssertUtils.isNull(goods, String.format("商品名称已存在，%s不存在！", pointsGoodsForm.getName()));
        }
        BeanUtils.copyProperties(pointsGoodsForm, entity);
        return pointsGoodsService.updateById(entity);
    }

    /**
     * 根据ID查询商品
     *
     * @param id 商品ID
     * @return 商品信息
     */
    public PointsGoodsResponse getInfo(Long id) {
        BcyPointsGoodsEntity entity = pointsGoodsService.getById(id);
        BcyAssertUtils.isNull(entity, String.format("该商品%s不存在！", id));
        PointsGoodsResponse response = new PointsGoodsResponse();
        BeanUtils.copyProperties(entity, response);
        return response;
    }

    /**
     * 积分兑换
     *
     * @param pointsGoodsOrderForm 兑换商品请求对象
     * @return 是否兑换成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean exchange(PointsGoodsOrderForm pointsGoodsOrderForm) {
        // 查询商品
        BcyPointsGoodsEntity goods = pointsGoodsService.getById(pointsGoodsOrderForm.getGoodsId());
        if (Objects.isNull(goods) || goods.getIsActive() == 0) {
            throw new BcyServiceException("商品不存在或未上架");
        }
        if (goods.getStock() < pointsGoodsOrderForm.getQuantity()) {
            throw new BcyServiceException("库存不足");
        }
        // 查询用户积分
        BcyUserPointsEntity userPoints = userPointsService.searchUserPoints(StpUtil.getLoginIdAsLong());
        if (userPoints == null || userPoints.getTotalPoints() < goods.getPointsCost() * pointsGoodsOrderForm.getQuantity()) {
            throw new BcyServiceException("积分不足");
        }
        // 扣减库存
        goods.setStock(goods.getStock() - pointsGoodsOrderForm.getQuantity());
        pointsGoodsService.updateById(goods);
        // 扣减积分
        userPoints.setTotalPoints(userPoints.getTotalPoints() - goods.getPointsCost() * pointsGoodsOrderForm.getQuantity());
        userPointsService.updateById(userPoints);
        // 保存兑换记录
        BcyPointsGoodsOrderEntity order = new BcyPointsGoodsOrderEntity();
        order.setUserId(StpUtil.getLoginIdAsLong());
        order.setGoodsId(pointsGoodsOrderForm.getGoodsId());
        order.setQuantity(pointsGoodsOrderForm.getQuantity());
        order.setTotalPoints(goods.getPointsCost() * pointsGoodsOrderForm.getQuantity());
        // 0待确认
        order.setStatus(PointsGoodsOrderStatusEnum.WAIT_CONFIRM.getCode());
        order.setAddressId(pointsGoodsOrderForm.getAddressId());
        return pointsGoodsOrderService.save(order);
    }
} 