package com.bcy.business.points;

import com.bcy.domain.form.points.PointsDeliveryForm;
import com.bcy.entity.points.BcyPointsDeliveryEntity;
import com.bcy.entity.points.BcyPointsGoodsOrderEntity;
import com.bcy.enums.PointsGoodsOrderStatusEnum;
import com.bcy.exception.BcyServiceException;
import com.bcy.service.points.PointsDeliveryService;
import com.bcy.service.points.PointsGoodsOrderService;
import com.bcy.utils.BcyAssertUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Objects;

/**
 * 业务层：发货
 */
@Service
public class BcyPointsDeliveryService {
    @Resource
    private PointsDeliveryService pointsDeliveryService;

    @Resource
    private PointsGoodsOrderService pointsGoodsOrderService;

    /**
     * 发货
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deliver(PointsDeliveryForm form) {
        // 校验订单
        BcyPointsGoodsOrderEntity order = pointsGoodsOrderService.getById(form.getOrderId());
        BcyAssertUtils.isNull(order, String.format("获取订单信息失败，%s不存在！", form.getOrderId()));
        if (!Objects.equals(PointsGoodsOrderStatusEnum.WAIT_DELIVERY.getCode(), order.getStatus())) {
            throw new BcyServiceException("订单状态不允许发货");
        }
        // 保存发货信息
        BcyPointsDeliveryEntity delivery = new BcyPointsDeliveryEntity();
        delivery.setOrderId(form.getOrderId());
        delivery.setUserId(order.getUserId());
        delivery.setAddressId(order.getAddressId());
        delivery.setDeliveryCompany(form.getDeliveryCompany());
        delivery.setDeliveryNo(form.getDeliveryNo());
        delivery.setDeliveryStatus(PointsGoodsOrderStatusEnum.DELIVERED.getCode()); // 已发货
        delivery.setDeliveryTime(new Date());
        pointsDeliveryService.save(delivery);
        // 更新订单状态
        order.setStatus(PointsGoodsOrderStatusEnum.DELIVERED.getCode());
        return pointsGoodsOrderService.updateById(order);
    }
} 