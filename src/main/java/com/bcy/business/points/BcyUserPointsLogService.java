package com.bcy.business.points;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcy.domain.request.PageRequest;
import com.bcy.domain.response.points.UserPointsLogResponse;
import com.bcy.entity.points.BcyPointsTaskEntity;
import com.bcy.entity.points.BcyUserPointsLogEntity;
import com.bcy.enums.PointsChangeTypeEnum;
import com.bcy.service.points.PointsTaskService;
import com.bcy.service.points.UserPointsLogService;
import com.bcy.utils.PageUtils;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 业务层：积分明细
 */
@Service
public class BcyUserPointsLogService {
    @Resource
    private UserPointsLogService userPointsLogService;
    @Resource
    private PointsTaskService pointsTaskService;

    /**
     * 积分明细分页列表
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    public PageUtils queryPage(PageRequest pageRequest) {

        IPage<BcyUserPointsLogEntity> iPage = userPointsLogService.queryPage(pageRequest);
        List<UserPointsLogResponse> responses = new ArrayList<>();
        List<BcyUserPointsLogEntity> logList = iPage.getRecords();
        if (CollectionUtils.isNotEmpty(logList)) {
            logList.forEach(item -> {
                UserPointsLogResponse response = new UserPointsLogResponse();
                BeanUtils.copyProperties(item, response);
                response.setChangeTypeName(PointsChangeTypeEnum.getDescriptionByCode(item.getChangeType()));
                BcyPointsTaskEntity task = pointsTaskService.getById(item.getRelatedId());
                if (Objects.nonNull(task)) {
                    response.setRelatedName(task.getName());
                }
                responses.add(response);
            });
        }
        return new PageUtils(responses, iPage.getTotal(), pageRequest.getLimit(), pageRequest.getPage());
    }
} 