package com.bcy.business.points;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.request.PageRequest;
import com.bcy.domain.response.page.points.PointsGoodsOrderPageResponse;
import com.bcy.domain.response.points.PointsGoodsOrderResponse;
import com.bcy.entity.points.BcyPointsGoodsEntity;
import com.bcy.entity.points.BcyPointsGoodsOrderEntity;
import com.bcy.entity.sys.SysUserEntity;
import com.bcy.service.points.PointsGoodsOrderService;
import com.bcy.service.points.PointsGoodsService;
import com.bcy.service.sys.IUserService;
import com.bcy.utils.BcyAssertUtils;
import com.bcy.utils.PageUtils;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 业务层：积分商品兑换记录
 */
@Service
public class BcyPointsGoodsOrderService {
    @Resource
    private PointsGoodsOrderService pointsGoodsOrderService;
    @Resource
    private PointsGoodsService pointsGoodsService;
    @Resource
    private IUserService userService;

    /**
     * 兑换记录分页
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    public PageUtils queryPage(PageRequest pageRequest) {
        IPage<BcyPointsGoodsOrderEntity> iPage = pointsGoodsOrderService.queryPage(pageRequest);
        List<PointsGoodsOrderPageResponse> responses = new ArrayList<>();
        List<BcyPointsGoodsOrderEntity> orderList = iPage.getRecords();
        if (CollectionUtils.isNotEmpty(orderList)) {
            orderList.forEach(item -> {
                PointsGoodsOrderPageResponse response = new PointsGoodsOrderPageResponse();
                BeanUtils.copyProperties(item, response);
                BcyPointsGoodsEntity goodsGoodsEntity = pointsGoodsService.getById(item.getGoodsId());
                if (Objects.nonNull(goodsGoodsEntity)) {
                    response.setGoodsName(goodsGoodsEntity.getName());
                }
                SysUserEntity user = userService.getById(item.getUserId());
                if (Objects.nonNull(user)) {
                    response.setUserName(user.getNickName());
                }
                responses.add(response);
            });
        }
        return new PageUtils(responses, iPage.getTotal(), pageRequest.getLimit(), pageRequest.getPage());
    }

    /**
     * 批量删除兑换记录
     */
    public boolean deleteByIds(IdsForm idsForm) {
        return pointsGoodsOrderService.removeByIds(idsForm.getIds());
    }

    /**
     * 根据ID查询兑换记录
     */
    public PointsGoodsOrderResponse getInfo(Long id) {
        BcyPointsGoodsOrderEntity entity = pointsGoodsOrderService.getById(id);
        BcyAssertUtils.isNull(entity, String.format("获取兑换记录失败，%s不存在！", id));
        PointsGoodsOrderResponse response = new PointsGoodsOrderResponse();
        BeanUtils.copyProperties(entity, response);
        return response;
    }

} 