package com.bcy.business.points;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.form.points.UserAddressForm;
import com.bcy.domain.request.PageRequest;
import com.bcy.domain.response.points.UserAddressResponse;
import com.bcy.entity.points.BcyUserAddressEntity;
import com.bcy.entity.sys.SysUserEntity;
import com.bcy.enums.YesOrNoEnum;
import com.bcy.exception.BcyServiceException;
import com.bcy.service.points.UserAddressService;
import com.bcy.service.sys.IUserService;
import com.bcy.utils.BcyAssertUtils;
import com.bcy.utils.PageUtils;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 业务层：用户收货地址
 */
@Service
public class BcyUserAddressService {
    @Resource
    private UserAddressService userAddressService;
    @Resource
    private IUserService userService;

    /**
     * 用户地址分页列表
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    public PageUtils queryPage(PageRequest pageRequest) {
        IPage<BcyUserAddressEntity> iPage = userAddressService.queryPage(pageRequest);
        List<UserAddressResponse> responses = new ArrayList<>();
        List<BcyUserAddressEntity> addressList = iPage.getRecords();
        if (CollectionUtils.isNotEmpty(addressList)) {
            addressList.forEach(item -> {
                UserAddressResponse response = new UserAddressResponse();
                BeanUtils.copyProperties(item, response);
                SysUserEntity user = userService.getById(item.getUserId());
                if (Objects.nonNull(user)) {
                    response.setUserName(user.getNickName());
                }
                responses.add(response);
            });
        }
        return new PageUtils(responses, iPage.getTotal(), pageRequest.getLimit(), pageRequest.getPage());
    }

    /**
     * 新增用户地址
     *
     * @param userAddressForm 添加用户地址
     * @return 是否添加成功
     */
    public boolean saveUserAddress(UserAddressForm userAddressForm) {
        BcyUserAddressEntity entity = new BcyUserAddressEntity();
        BeanUtils.copyProperties(userAddressForm, entity);
        return userAddressService.save(entity);
    }

    /**
     * 批量删除用户地址
     *
     * @param idsForm id数组
     * @return true/false
     */
    public boolean deleteByIds(IdsForm idsForm) {
        return userAddressService.removeBatchByIds(idsForm.getIds());
    }

    /**
     * 更新用户地址
     *
     * @param userAddressForm 更新用户地址
     * @return 是否更新成功
     */
    public boolean updateUserAddress(UserAddressForm userAddressForm) {
        BcyUserAddressEntity entity = userAddressService.getById(userAddressForm.getId());
        BcyAssertUtils.isNull(entity, String.format("用户地址信息%s不存在！", userAddressForm.getId()));
        BeanUtils.copyProperties(userAddressForm, entity);
        return userAddressService.updateById(entity);
    }

    /**
     * 根据ID查询用户地址
     *
     * @param id 用户地址ID
     * @return 用户地址
     */
    public UserAddressResponse getInfo(Long id) {
        BcyUserAddressEntity entity = userAddressService.getById(id);
        BcyAssertUtils.isNull(entity, String.format("用户地址信息%s不存在！", id));
        UserAddressResponse response = new UserAddressResponse();
        BeanUtils.copyProperties(entity, response);
        return response;
    }

    /**
     * 设置默认用户地址
     *
     * @param id 用户地址ID
     * @return true/false
     */
    public boolean updateDefault(Long id) {
        BcyUserAddressEntity entity = userAddressService.getById(id);
        BcyAssertUtils.isNull(entity, String.format("用户地址信息%s不存在！", id));
        List<BcyUserAddressEntity> list = userAddressService.listByUserId(entity.getUserId());
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(item -> item.setIsDefault(YesOrNoEnum.NO.getCode()));
            userAddressService.updateBatchById(list);
        }
        entity.setIsDefault(YesOrNoEnum.YES.getCode());
        return userAddressService.updateById(entity);
    }
} 