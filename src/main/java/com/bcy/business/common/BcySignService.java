package com.bcy.business.common;

import cn.dev33.satoken.stp.StpUtil;
import com.bcy.domain.form.common.SignDateForm;
import com.bcy.entity.points.BcyUserPointsEntity;
import com.bcy.entity.points.BcyUserPointsLogEntity;
import com.bcy.exception.BcyServiceException;
import com.bcy.service.SignService;
import com.bcy.service.points.UserPointsLogService;
import com.bcy.service.points.UserPointsService;
import com.bcy.enums.PointsChangeTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 签到相关服务
 *
 * <AUTHOR>
 * @since 2024/01/02-21:38
 */
@Service
@Slf4j
public class BcySignService {
    @Resource
    private SignService signService;
    @Resource
    private UserPointsService userPointsService;
    @Resource
    private UserPointsLogService userPointsLogService;

    /**
     * 用户签到或者补签
     *
     * @param signDateForm 签到日期表单
     */
    @Transactional(rollbackFor = Exception.class)
    public void userSign(SignDateForm signDateForm) {
        if (Objects.isNull(signDateForm.getDate())) {
            signDateForm.setDate(new Date());
        }
        LocalDateTime localDateTime = signDateForm.getDate().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        Long userId = StpUtil.getLoginIdAsLong();
        boolean isSign = signService.isSign(userId, localDateTime);
        if (isSign) {
            throw new BcyServiceException("当天已签到！");
        }
        signService.sign(userId, localDateTime);
        // ====== 连续签到奖励逻辑 ======
        int signCount = signService.signCount(userId, localDateTime);
        int reward = 10;
        if (signCount >= 7) {
            reward = 30;
        } else if (signCount >= 5) {
            reward = 20;
        } else if (signCount >= 3) {
            reward = 15;
        }
        BcyUserPointsEntity pointsEntity = userPointsService.searchUserPoints(userId);
        int before = 0;
        if (Objects.isNull(pointsEntity)) {
            pointsEntity = new BcyUserPointsEntity();
            pointsEntity.setUserId(userId);
            pointsEntity.setTotalPoints(reward);
            userPointsService.save(pointsEntity);
        } else {
            before = pointsEntity.getTotalPoints() == null ? 0 : pointsEntity.getTotalPoints();
            pointsEntity.setTotalPoints(before + reward);
            userPointsService.updateById(pointsEntity);
        }
        // 记录积分明细
        BcyUserPointsLogEntity log = new BcyUserPointsLogEntity();
        log.setUserId(userId);
        log.setChangeType(PointsChangeTypeEnum.SIGN.getCode());
        log.setChangeValue(reward);
        log.setBeforePoints(before);
        log.setAfterPoints(before + reward);
        log.setRemark("每日签到奖励，连续签到" + signCount + "天");
        userPointsLogService.save(log);
    }

    /**
     * 当月签到记录
     *
     * @return 记录数组
     */
    public Map<String, Boolean> record() {
        LocalDateTime localDateTime = LocalDateTime.now();
        Long user = StpUtil.getLoginIdAsLong();
        return signService.signInRecord(user, localDateTime);
    }

    /**
     * 当月连续签到次数
     *
     * @return 连续次数
     */
    public Integer count() {
        LocalDateTime localDateTime = LocalDateTime.now();
        Long user = StpUtil.getLoginIdAsLong();
        return signService.signCount(user, localDateTime);
    }
}
